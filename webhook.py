import requests
import json
import os
import socket
import re
import tempfile
from bs4 import BeautifulSoup
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Query, Body, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Dict, Any, Union, Tuple
from pydantic import BaseModel
import uvicorn
import logging
import argparse
from openai import OpenAI
from datetime import datetime
from dotenv import load_dotenv
import threading
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()
# 认证信息默认值
DEFAULT_ACCESS_TOKEN = ""
DEFAULT_JWT_TOKEN = ""
DEFAULT_GITLAB_TOKEN = ""

# 全局变量，用于保存请求结果
saved_descriptions = []
saved_branches = []
saved_commit_diffs = []
commit_id_latest = None
global branch_name


# 使用lifespan进行启动和关闭处理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    logger.info("应用程序启动中...")
    yield
    # 关闭时执行
    logger.info("应用程序关闭中...")


# 创建FastAPI应用
app = FastAPI(
    title="需求与提交差异API",
    description="获取指定迭代和系统的需求描述与GitLab提交差异",
    lifespan=lifespan
)

# 添加CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 定义请求模型
class RequirementItem(BaseModel):
    id: int
    name: str
    description: str  # HTML格式的描述，可能包含文本和图片URL
    # 其他字段可以设置为可选
    systemId: Optional[int] = None
    iterationId: Optional[int] = None
    # 其他字段...


class RequirementRequest(BaseModel):
    uuid: str  # 新增uuid字段，用于回调标识
    systemId: int
    iterationId: int
    requirementList: List[RequirementItem]
    access_token: Optional[str] = DEFAULT_ACCESS_TOKEN
    jwt_token: Optional[str] = DEFAULT_JWT_TOKEN
    gitlab_token: str = DEFAULT_GITLAB_TOKEN
    analyze: Optional[bool] = True
    env: Optional[str] = "prod"  # 新增env字段，默认为"prod"


# 定义响应模型
class BranchInfo(BaseModel):
    # 修改字段类型，支持整数或字符串
    gitlabId: Optional[Union[int, str]] = None
    branch: Optional[str] = None
    projectName: Optional[str] = None


class DiffInfo(BaseModel):
    project_name: str
    gitlab_id: Union[int, str]
    commit_id: str
    diffs: List[Dict[str, Any]]


class RequirementAnalysis(BaseModel):
    requirement_name: str
    analysis_result: str


class RequirementResponse(BaseModel):
    descriptions: List[Dict[str, Any]]
    branches: List[BranchInfo]
    diffs: List[DiffInfo]
    requirement_analyses: Optional[List[RequirementAnalysis]] = None
    analysis: Optional[Dict[str, Any]] = None


# 工具函数：获取GitLab分支信息
async def get_gitlab_branch_info(iteration_id: int, system_id: int, token: str = DEFAULT_ACCESS_TOKEN,
                                 jwt_token: str = DEFAULT_JWT_TOKEN):
    """获取GitLab分支信息"""

    # 声明全局变量
    global saved_branches

    # API URL
    url = f""

    # 添加认证头
    headers = {}
    cookies = {}

    if token:
        headers["Authorization"] = f"Bearer {token}"
        headers["Access_token"] = token
        cookies["access_token"] = token

    if jwt_token:
        headers["Jwt_token"] = jwt_token
        cookies["jwt_token"] = jwt_token

    # 打印完整请求信息用于调试
    logger.info(f"请求URL: {url}")
    logger.info(f"请求头: {headers}")

    try:
        # 发送请求
        logger.info(f"正在请求分支信息: {url}")
        response = requests.get(url, headers=headers, cookies=cookies)

        # 记录完整响应内容
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应头: {response.headers}")

        if response.status_code != 200:
            logger.error(f"获取分支信息失败: {response.status_code}, 响应内容: {response.text}")
            raise HTTPException(status_code=response.status_code, detail=f"获取GitLab分支信息失败: {response.text}")

        try:
            data = response.json()
            logger.info(f"API响应: {json.dumps(data)[:200]}...")
        except json.JSONDecodeError as e:
            logger.error(f"解析响应JSON失败: {str(e)}, 响应内容: {response.text[:500]}")
            return []

        # 检查是否成功
        if "code" in data and data["code"] == 401:
            logger.error(f"认证失败: {data.get('msg', '未知错误')}")
            raise HTTPException(status_code=401, detail=f"认证失败: {data.get('msg', '未知错误')}")

        if "data" not in data:
            logger.warning(f"响应中没有'data'字段: {json.dumps(data)}")
            return []

        # 防止data字段为None
        branch_info = data["data"] or []
        if not isinstance(branch_info, list):
            logger.warning(f"分支信息不是列表格式: {type(branch_info)}, 值: {branch_info}")
            return []

        # 提取分支信息
        branches = []
        for item in branch_info:
            if not isinstance(item, dict):
                logger.warning(f"分支项不是字典格式: {type(item)}, 值: {item}")
                continue
            branch = {
                "gitlabId": item.get("gitlabId"),
                "branch": item.get("branch"),
                "projectName": item.get("projectName"),
            }
            global branch_name_cos
            branch_name_cos = item.get("branch")

            branches.append(branch)

        logger.info(f"分支信息详情: {json.dumps(branches)}")

        # 保存到全局变量
        saved_branches = branches

        return branches
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取GitLab分支信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取GitLab分支信息时出错: {str(e)}")


# 工具函数：获取GitLab提交的差异信息
async def get_gitlab_commit_diff(gitlab_id: Union[int, str], commit_id: str, gitlab_token: str = DEFAULT_GITLAB_TOKEN):
    """获取GitLab提交的差异信息"""

    # 声明全局变量
    global saved_commit_diffs

    # GitLab API URL
    url = f""
    headers = {"PRIVATE-TOKEN": gitlab_token}

    logger.info(f"GitLab API请求: URL={url}, Token={gitlab_token[:4]}***")

    try:
        # 发送请求
        logger.info(f"正在获取提交差异: {url}")
        response = requests.get(url, headers=headers)

        # 记录响应状态
        logger.info(f"GitLab API响应状态码: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"获取提交差异失败: {response.status_code}, 响应: {response.text[:500]}")
            raise HTTPException(
                status_code=response.status_code,
                detail=f"获取差异信息失败: {response.text}"
            )

        try:
            diff_data = response.json()
            logger.info(f"成功获取差异信息，包含 {len(diff_data)} 项差异")
        except json.JSONDecodeError as e:
            logger.error(f"解析GitLab响应失败: {str(e)}, 响应: {response.text[:500]}")
            return []

        # 保存到全局变量
        saved_commit_diffs = diff_data

        return diff_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取差异信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取差异信息时出错: {str(e)}")


# 新增工具函数：获取分支最新的commit ID
async def get_latest_commit_id(gitlab_id: Union[int, str], branch_name: str, gitlab_token: str = DEFAULT_GITLAB_TOKEN):
    """获取GitLab特定分支的最新commit ID"""

    # GitLab API URL
    url = f""
    headers = {"PRIVATE-TOKEN": gitlab_token}

    logger.info(f"获取commit ID请求: URL={url}, Token={gitlab_token[:4]}***")

    try:
        # 发送请求
        logger.info(f"正在获取分支 {branch_name} 的最新commit ID: {url}")
        response = requests.get(url, headers=headers)

        logger.info(f"获取commit ID响应状态码: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"获取分支最新commit ID失败: {response.status_code}, 响应: {response.text[:500]}")
            raise HTTPException(
                status_code=response.status_code,
                detail=f"获取分支最新commit ID失败: {response.text}"
            )

        try:
            branch_data = response.json()
            logger.info(f"分支信息响应: {json.dumps(branch_data)[:200]}")
        except json.JSONDecodeError as e:
            logger.error(f"解析分支信息响应失败: {str(e)}, 响应: {response.text[:500]}")
            raise HTTPException(status_code=500, detail=f"解析分支信息响应失败: {str(e)}")
        global commit_id_latest

        # 从响应中提取最新的commit ID
        if "commit" in branch_data and "id" in branch_data["commit"]:
            commit_id = branch_data["commit"]["id"]
            commit_id_latest = commit_id
            logger.info(f"成功获取分支 {branch_name} 的最新commit ID: {commit_id}")
            return commit_id
        else:
            logger.error(f"响应中没有commit ID信息: {json.dumps(branch_data)}")
            raise HTTPException(status_code=500, detail="响应中没有commit ID信息")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分支最新commit ID时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分支最新commit ID时出错: {str(e)}")


# 大模型分析函数：分析需求和代码提交之间的关联性
async def analyze_requirements_with_llm(requirement_analyses, branches, commit_diffs):
    """
    使用大模型分析需求描述和代码提交之间的关联性

    Args:
        requirement_analyses: 多模态模型对每个需求的分析结果
        branches: 分支信息列表
        commit_diffs: 代码提交差异

    Returns:
        analysis_result: 大模型分析结果
    """
    # 验证输入数据
    if not requirement_analyses:
        logger.warning("没有需求分析数据，无法进行综合分析")
        return {"success": False, "error": "没有需求分析数据"}

    if not commit_diffs:
        logger.warning("没有提交差异数据，无法进行全面分析")

    # 构建需求分析文档
    requirement_analysis_text = ""
    for analysis in requirement_analyses:
        if "requirement_name" in analysis and "analysis_result" in analysis:
            requirement_analysis_text += f"需求名称: {analysis['requirement_name']}\n"
            requirement_analysis_text += f"需求分析: {analysis['analysis_result']}\n\n"

    if not requirement_analysis_text.strip():
        logger.warning("需求分析内容为空，无法进行综合分析")
        return {"success": False, "error": "需求分析内容为空"}

    # 构建提交详情
    commit_details = json.dumps(commit_diffs, ensure_ascii=False, indent=2)
    branches_info = json.dumps(branches, ensure_ascii=False, indent=2)

    # 构建系统提示词
    system_prompt = """
    作为自动驾驶研发流程质量控制专家，请严格评估需求分析与代码提交之间的关联性，并提供详细分析。评估应包含以下几个方面：

    1. 功能关联性：代码变更是否直接实现了需求分析中描述的功能点？请具体指出哪些代码变更对应哪些需求点。

    2. 技术逻辑关联性：代码实现的技术路径是否符合需求分析中的技术要求或隐含的技术路径？

    3. 范围适当性：代码变更的范围是否恰当？是否存在与需求无关的文件或模块被修改？

    4. 潜在风险：代码变更是否可能对需求分析未提及的功能产生影响？

    5. 综合判断：基于以上分析，给出明确的关联性结论（相关/无关）。

    请注意：分析必须基于事实和代码逻辑，避免主观臆断。如有不确定之处，请明确指出并说明原因。
    请严格按照以下Markdown格式输出分析报告：

    ### 关联性评估分析
    #### **1. 功能关联性** 
    (请严格引用每个需求名称requirement_name，分析对应的代码变更)
    - **需求名称: **  
    - **需求名称: ** 
    ...

    #### **2. 技术逻辑关联性**  

    #### **3. 范围适当性**  

    #### **4. 潜在风险** 

    #### **5. 综合判断**  
    **结论：** [在此处必须且只能填入以下两个选项之一："相关"、"无关"]
    """

    # 构建用户消息
    message = f"""
    请严谨且详细地分析以下数据并评估它们之间的关联性：
    【需求分析文档】
    {requirement_analysis_text}

    【代码提交详情】
    代码差异：{commit_details}
    """

    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key="",  # 中台APIkey
            base_url="",  # 调用地址
        )

        logger.info("开始调用大模型进行需求与代码提交关联性综合分析...")

        # 创建聊天完成请求
        completion = client.chat.completions.create(
            model="DeepSeek-R1-0528",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message},
            ],
            temperature=0,
            stream=False,  # 不使用流式输出
        )

        # 获取分析结果
        analysis_content = completion.choices[0].message.content

        # 处理结果，删除<think>标签内容
        if "<think>" in analysis_content and "</think>" in analysis_content:
            logger.info("检测到大模型思考内容，正在移除...")
            import re
            # 使用正则表达式删除<think>和</think>之间的内容（包括标签本身）
            processed_content = re.sub(r'<think>.*?</think>', '', analysis_content, flags=re.DOTALL)
            # 清理可能的多余空行
            processed_content = re.sub(r'\n{3,}', '\n\n', processed_content.strip())
            logger.info("已清理大模型思考内容")
        elif "<think>" in analysis_content:
            # 处理只有开始标签没有结束标签的情况
            logger.info("检测到不完整的大模型思考内容，正在移除...")
            import re
            # 从<think>开始到第一个标题标记(#)之间的内容全部移除
            processed_content = re.sub(r'<think>.*?(?=#)', '', analysis_content, flags=re.DOTALL)
            # 如果没有标题标记，则移除从<think>到内容结束
            # if processed_content == analysis_content:
            #     processed_content = re.sub(r'<think>.*', '', analysis_content, flags=re.DOTALL)
            # 清理可能的多余空行
            processed_content = re.sub(r'\n{3,}', '\n\n', processed_content.strip())
            logger.info("已清理不完整的大模型思考内容")
        else:
            processed_content = analysis_content

        logger.info("需求与代码提交关联性综合分析完成")

        # 保存分析结果到文件
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # filename = f"综合分析报告_{timestamp}.txt"

        # try:
        #     # 确保文件夹存在
        #     os.makedirs("analysis_reports", exist_ok=True)
        #
        #     # 保存文件
        #     file_path = os.path.join("analysis_reports", filename)
        #     with open(file_path, "w", encoding="utf-8") as f:
        #         f.write(f"需求与代码提交关联性综合分析报告\n")
        #         f.write(f"生成时间: {timestamp}\n")
        #         f.write("\n" + "=" * 50 + "\n\n")
        #         f.write(processed_content)
        #
        #     logger.info(f"综合分析报告已保存到文件: {file_path}")
        # except Exception as e:
        #     logger.error(f"保存综合分析报告失败: {str(e)}")

        return {
            "success": True,
            "analysis_result": processed_content,
            # "raw_result": analysis_content  # 保留原始结果，以备需要
        }

    except Exception as e:
        logger.error(f"大模型综合分析出错: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


# 工具函数：从HTML中提取文本和图片URL
def extract_text_and_images_from_html(html_content: str) -> Tuple[str, List[str]]:
    """
    从HTML内容中提取纯文本和图片URL列表

    Args:
        html_content: HTML格式的字符串

    Returns:
        Tuple[str, List[str]]: 包含纯文本和图片URL列表的元组
    """
    if not html_content or not isinstance(html_content, str):
        return "", []

    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # 提取所有图片URL
    img_urls = []
    for img in soup.find_all('img'):
        if img.get('src'):
            img_urls.append(img['src'])

    # 删除所有img标签，只保留文本
    for img in soup.find_all('img'):
        img.decompose()

    # 获取纯文本内容
    text_content = soup.get_text(separator="\n").strip()

    logger.info(f"从HTML中提取到文本: {text_content[:100]}...和 {len(img_urls)} 张图片")
    return text_content, img_urls


# 工具函数：使用多模态大模型分析需求描述
async def analyze_requirement_with_multimodal_model(
        requirement_name: str,
        text_content: str,
        image_urls: List[str]
) -> Dict[str, Any]:
    """
    使用多模态大模型分析包含文本和图片的需求描述

    Args:
        requirement_name: 需求名称
        text_content: 需求文本描述
        image_urls: 需求中包含的图片URL列表

    Returns:
        Dict[str, Any]: 大模型分析结果
    """
    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key="",  # 中台APIkey
            base_url="",  # 调用地址
        )

        logger.info(f"开始使用多模态模型分析需求: {requirement_name}")

        # 构建系统提示词
        system_prompt = """
        你是自动驾驶研发流程质量控制专家，请详细分析用户提供的需求描述（包括文本和图片）。
        分析需要包含以下几点：
        1. 需求的主要功能点和目标
        2. 需求中的技术要点和难点
        3. 需求的实现可能涉及的代码模块和文件
        4. 需求的实现可能带来的影响和风险

        请基于文本和图片内容，尽可能详细地分析需求。如果图片显示了界面设计、流程图或其他技术细节，请详细描述并分析其含义。
        """

        # 构建消息内容
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": []}
        ]

        # 添加文本内容
        user_content = messages[1]["content"]
        user_content.append({
            "type": "text",
            "text": f"需求名称: {requirement_name}\n\n需求描述: {text_content}"
        })
        flag = False  # 默认为False，表示不包含图片
        # 添加图片URL（如果有）
        for img_url in image_urls:
            flag = True  # 如果执行了循环体，说明包含图片
            user_content.append({
                "type": "image_url",
                "image_url": {"url": img_url}
            })

        logger.info(f"向大模型发送请求: 文本长度={len(text_content)}, 图片数量={len(image_urls)}")
        # 根据是否包含图片选择不同的模型
        use_model = "InternVL3-38B" if flag else "Qwen3-30B-A3B"
        logger.info(f"选择使用模型: {use_model}")

        try:
            # 创建聊天完成请求
            completion = client.chat.completions.create(
                model=use_model,  # 使用支持图像分析的多模态模型
                messages=messages,
                temperature=0,
                stream=False,
                max_tokens=30000,
            )

            # 获取分析结果
            analysis_content = completion.choices[0].message.content

            # 处理结果，删除<think>标签内容
            if "<think>" in analysis_content and "</think>" in analysis_content:
                processed_content = re.sub(r'<think>.*?</think>', '', analysis_content, flags=re.DOTALL)
                processed_content = re.sub(r'\n{3,}', '\n\n', processed_content.strip())
            else:
                processed_content = analysis_content

            logger.info(f"多模态模型分析完成: {processed_content[:100]}...")

            return {
                "requirement_name": requirement_name,
                "analysis_result": processed_content
            }
        except Exception as e:
            logger.error(f"多模态模型调用失败!!!!!!!")


    except Exception as e:
        logger.error(f"多模态模型分析出错: {str(e)}")
        return {
            "requirement_name": requirement_name,
            "analysis_result": f"分析失败: {str(e)}"
        }


# 工具函数：将响应保存到腾讯云COS
async def save_response_to_cos(response_data):
    """将API响应保存到腾讯云COS"""
    try:
        from qcloud_cos import CosConfig
        from qcloud_cos import CosS3Client

        # 从环境变量获取COS配置
        COS_SECRET_ID = os.getenv("COS_SECRET_ID")
        COS_SECRET_KEY = os.getenv("COS_SECRET_KEY")
        COS_BUCKET = os.getenv("COS_BUCKET")
        COS_REGION = os.getenv("COS_REGION")
        COS_BASE_URL = os.getenv("COS_BASE_URL")

        # 检查必要的环境变量
        if not all([COS_SECRET_ID, COS_SECRET_KEY, COS_BUCKET, COS_REGION]):
            logger.error("缺少必要的COS环境变量配置")
            return None

        # 创建COS配置
        config = CosConfig(
            Region=COS_REGION,
            SecretId=COS_SECRET_ID,
            SecretKey=COS_SECRET_KEY
        )
        client = CosS3Client(config)

        # 从response_data中提取analysis_result内容
        content_to_save = ""
        if "analysis" in response_data and "analysis_result" in response_data["analysis"]:
            content_to_save = response_data["analysis"]["analysis_result"]
        else:
            # 如果没有analysis_result，则保存整个response_data为JSON
            content_to_save = json.dumps(response_data, ensure_ascii=False, indent=2)

        # 创建临时文件
        file_suffix = ".md" if "analysis" in response_data and "analysis_result" in response_data[
            "analysis"] else ".json"
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=file_suffix, encoding='utf-8') as temp_file:
            temp_file.write(content_to_save)
            temp_path = temp_file.name

        # 设置文件名，使用commit_id_latest作为文件名
        global commit_id_latest
        file_key = f"AI-code_merge_check/{branch_name_cos}_{commit_id_latest}.md"

        # 上传文件到COS
        logger.info(f"正在上传响应到COS: {file_key}")
        upload_response = client.upload_file(
            Bucket=COS_BUCKET,
            Key=file_key,
            LocalFilePath=temp_path
        )

        # 删除临时文件
        os.unlink(temp_path)

        # 构建访问URL
        cos_url = f"{COS_BASE_URL}/{file_key}" if COS_BASE_URL else None

        logger.info(f"成功将响应保存到COS: {file_key}")
        return {"file_key": file_key, "url": cos_url}

    except Exception as e:
        logger.error(f"保存响应到COS失败: {str(e)}")
        return None


# 工具函数：发送回调通知
async def send_callback(uuid: str, cos_address: str):
    """发送完成回调到后端系统"""
    callback_url = "http://gateway.ee-private-dev1.myghost.zhidaoauto.com/openapi/daily/callBack/codeRequirementDiff"

    callback_data = {
        "uuid": uuid,
        "cosAddress": cos_address
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        logger.info(f"发送回调请求: {callback_data}")
        response = requests.post(
            callback_url,
            json=callback_data,
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            logger.info(f"回调成功: UUID={uuid}, 状态码={response.status_code}")
        else:
            logger.error(f"回调失败: UUID={uuid}, 状态码={response.status_code}, 响应={response.text}")
            raise Exception(f"回调返回错误状态码: {response.status_code}")

    except Exception as e:
        logger.error(f"回调请求异常: UUID={uuid}, 错误={str(e)}")
        raise


# 工具函数：从分析报告中提取明确的关联性结论
def extract_correlation_conclusion(analysis_result):
    """从分析报告中提取明确的关联性结论"""

    if not analysis_result:
        return "未能确定"

    # 直接查找"**结论：**"及其变体，匹配"相关"或"无关"
    conclusion_patterns = [
        r"\*\*结论：\*\*\s*([相关|无关]+)",
        r"\*\*结论:\*\*\s*([相关|无关]+)",
        r"结论：\s*([相关|无关]+)",
        r"结论:\s*([相关|无关]+)",
        r"\*\*结论\*\*：\s*([相关|无关]+)",
        r"\*\*结论\*\*:\s*([相关|无关]+)"
    ]

    for pattern in conclusion_patterns:
        match = re.search(pattern, analysis_result)
        if match:
            conclusion = match.group(1).strip()
            if conclusion in ["相关", "无关"]:
                return conclusion

    # 如果正则表达式无法匹配，使用更简单的字符串查找方法
    if "**结论：** 相关" in analysis_result or "**结论：**相关" in analysis_result:
        return "相关"
    elif "**结论：** 无关" in analysis_result or "**结论：**无关" in analysis_result:
        return "无关"
    elif "结论：相关" in analysis_result or "结论:相关" in analysis_result:
        return "相关"
    elif "结论：无关" in analysis_result or "结论:无关" in analysis_result:
        return "无关"

    # 如果上述关键词未找到，则返回默认值
    return "未能确定"


# 处理前端传来的结构化数据
@app.post("/api/requirements", response_model=None)
async def post_api_requirements_endpoint(
        request: RequirementRequest = Body(...),
):
    """
    接收前端传来的结构化数据，并异步进行分析

    立即返回成功响应，然后在后台异步执行实际任务

    请求参数:
    - **systemId**: 系统ID
    - **iterationId**: 迭代ID
    - **requirementList**: 需求列表，包含HTML格式的description字段(可包含文本和图片)
    - **analyze**: 是否进行大模型分析，默认为true

    响应内容:
    - **descriptions**: 提取的需求描述(包含文本和图片URL)
    - **branches**: 分支信息
    - **diffs**: 代码提交差异
    - **requirement_analyses**: 每个需求的多模态分析结果
    - **analysis**: 综合分析结果
    - 立即返回: { "code": 200, "data": null }
    - 实际处理在后台异步执行
    """
    # 记录请求开始
    logger.info(f"接收到请求: UUID={request.uuid}, 系统ID={request.systemId}, 迭代ID={request.iterationId}")
    logger.info(f"请求完整信息: {request.model_dump()}")

    try:
        # 创建一个独立的事件循环用于异步处理
        def run_async_task(req):
            # 设置新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 运行异步任务
                loop.run_until_complete(process_requirements_async(req))
            except Exception as e:
                logger.error(f"异步处理任务失败: {str(e)}")
            finally:
                # 关闭事件循环
                loop.close()

        # 启动一个新线程来处理异步任务
        thread = threading.Thread(
            target=run_async_task,
            args=(request,),
            daemon=True  # 设置为守护线程，当主线程结束时会自动结束
        )
        thread.start()

        logger.info("已启动后台线程进行异步处理")

        # 立即返回成功响应
        return {"code": 200, "data": None}
    except Exception as e:
        logger.error(f"启动异步处理线程失败: {str(e)}")
        return {"code": 500, "message": "服务器内部错误，无法启动异步处理"}


# 异步处理任务的函数
async def process_requirements_async(request: RequirementRequest):
    """
    异步处理需求分析任务
    这包含了原来在endpoint中的所有逻辑
    """
    # 声明全局变量
    # 记录请求开始时间
    from datetime import datetime

    request_start_time = datetime.now()
    logger.info(f"开始异步处理请求，记录开始时间: {request_start_time}")
    global saved_descriptions

    logger.info(f"异步处理: UUID={request.uuid}, 系统ID: {request.systemId}, 迭代ID: {request.iterationId}")

    # 1. 从请求体提取需求描述和图片
    descriptions = []
    requirement_analyses = []

    # 1.1 处理每个需求
    for req in request.requirementList:
        req_name = req.name
        req_desc = req.description

        # 从HTML中提取文本和图片URL
        text_content, image_urls = extract_text_and_images_from_html(req_desc)

        # 记录提取到的需求信息
        descriptions.append({
            "name": req_name,
            "description": text_content,
            "image_urls": image_urls
        })

        logger.info(f"处理需求: {req_name}, 提取到文本长度: {len(text_content)}, 图片数量: {len(image_urls)}")

        # 使用多模态模型分析需求（如果analyze为True）
        if request.analyze:
            try:
                analysis = await analyze_requirement_with_multimodal_model(
                    requirement_name=req_name,
                    text_content=text_content,
                    image_urls=image_urls
                )
                requirement_analyses.append(analysis)

                logger.info(f"需求 '{req_name}' 的多模态分析完成")
            except Exception as e:
                logger.error(f"分析需求 '{req_name}' 时出错: {str(e)}")
                requirement_analyses.append({
                    "requirement_name": req_name,
                    "analysis_result": f"分析失败: {str(e)}"
                })
    # with open("requirement_analyses.txt","w") as f:
    #     f.write(str(requirement_analyses))
    logger.info(f"共提取 {len(descriptions)} 个需求描述")

    # 保存到全局变量（仅保存文本部分，不包括图片URL）
    saved_descriptions = [{"name": d["name"], "description": d["description"]} for d in descriptions]

    # 2. 获取GitLab分支信息
    try:
        branches = await get_gitlab_branch_info(
            request.iterationId,
            request.systemId,
            request.access_token,
            request.jwt_token
        )
    except Exception as e:
        logger.error(f"获取分支信息失败: {str(e)}")
        branches = []

    if not branches:
        logger.error("没有成功获取到分支信息!")

    # 3. 获取每个分支的提交差异
    diffs = []
    if branches:
        logger.info("开始获取分支差异信息")
        for branch in branches:
            if branch.get('gitlabId') and branch.get('branch'):  # 使用get方法安全访问
                logger.info(f"处理分支: gitlabId={branch.get('gitlabId')}, branch={branch.get('branch')}")
                try:
                    # 先获取最新的commit ID
                    latest_commit_id = await get_latest_commit_id(
                        branch['gitlabId'],
                        branch['branch'],
                        request.gitlab_token
                    )

                    logger.info(f"获取到最新commit ID: {latest_commit_id}")

                    # 使用最新的commit ID获取差异
                    diff_data = await get_gitlab_commit_diff(
                        branch['gitlabId'],
                        latest_commit_id,
                        request.gitlab_token
                    )

                    diff_info = {
                        "project_name": branch.get('projectName', '未知项目'),
                        "gitlab_id": branch['gitlabId'],
                        "commit_id": latest_commit_id,
                        "diffs": diff_data
                    }
                    diffs.append(diff_info)
                    logger.info(f"成功添加差异信息: project={diff_info['project_name']}, commit={latest_commit_id[:8]}")
                except HTTPException as e:
                    # 如果获取某个提交的差异失败，记录错误但继续处理下一个
                    logger.warning(f"获取分支 {branch.get('branch', '未知分支')} 的差异失败: {str(e)}")
                    continue
                except Exception as e:
                    logger.error(f"处理分支差异时出错: {str(e)}")
                    continue
    else:
        logger.warning("没有分支信息，无法获取代码差异")

    # 4. 构建基本响应
    # response = {
    #     "descriptions": descriptions,
    #     "branches": branches,
    #     "diffs": diffs,
    #     "requirement_analyses": requirement_analyses,  # 添加每个需求的多模态分析结果
    #     "debug_info": {
    #         "systemId": request.systemId,
    #         "iterationId": request.iterationId
    #     }
    # }
    response = {
        "branches": branches,
    }
    # response = {}
    # 5. 如果需要进行整体分析，并且有提交差异数据
    if request.analyze and diffs and requirement_analyses:
        logger.info("开始进行需求与代码提交关联性分析...")
        try:
            # 使用多模态分析结果和代码差异进行综合分析
            analysis_result = await analyze_requirements_with_llm(
                requirement_analyses,  # 使用多模态分析结果代替原始描述
                saved_branches,
                saved_commit_diffs
            )
            # 将分析结果添加到响应中
            response["analysis"] = analysis_result
            logger.info("综合分析完成 ✓")
        except Exception as e:
            logger.error(f"大模型综合分析出错: {str(e)}")
            response["analysis"] = {
                "success": False,
                "error": f"大模型综合分析出错: {str(e)}"
            }
    elif not diffs and request.analyze:
        logger.warning("没有找到代码差异数据，无法进行综合分析")
        response["analysis"] = {
            "success": False,
            "error": "没有找到代码差异数据，无法进行综合分析"
        }

    # 将响应保存到腾讯云COS
    try:
        logger.info("开始将响应保存到腾讯云COS...")
        cos_result = await save_response_to_cos(response)
        if cos_result:
            # 将COS存储信息添加到响应中
            response["cos_storage"] = cos_result
            logger.info(f"响应已成功存储到COS: {cos_result.get('file_key')}")
        else:
            logger.warning("响应存储到COS失败")
    except Exception as e:
        logger.error(f"保存响应到COS过程中出错: {str(e)}")
        # 添加错误信息到响应中，但不影响主要功能
        response["cos_storage_error"] = str(e)

    # 将数据保存到数据库中
    try:
        logger.info("开始将响应保存到数据库...")

        # 导入数据库相关模块
        from create_db import get_session, CodeMergeAICheck
        from datetime import datetime

        # 准备数据库字段数据
        current_time = datetime.now()

        # 需求信息：将saved_descriptions转换为JSON字符串
        requirement_info = json.dumps(saved_descriptions, ensure_ascii=False) if saved_descriptions else None

        # 提交差异信息：将saved_commit_diffs转换为JSON字符串
        commit_diff_info = json.dumps(saved_commit_diffs, ensure_ascii=False) if saved_commit_diffs else None

        # COS地址：从cos_result中获取URL
        cos_address = cos_result.get('url') if cos_result else None

        # 分析报告：从response中获取analysis_result
        analysis_report = None
        if "analysis" in response and "analysis_result" in response["analysis"]:
            analysis_report = response["analysis"]["analysis_result"]

        # 分析成功状态：从response中获取success字段
        analysis_success = False
        if "analysis" in response and "success" in response["analysis"]:
            analysis_success = response["analysis"]["success"]

        # 是否通过：需要从分析报告中解析（默认为None，表示未确定）
        is_approved = None

        # 使用提取函数获取明确结论
        correlation_conclusion = None
        if analysis_report:
            # 使用提取函数获取明确结论
            correlation_conclusion = extract_correlation_conclusion(analysis_report)

            # 根据结论设置审批状态
            if correlation_conclusion == "高度相关":
                is_approved = True
            elif correlation_conclusion == "部分相关":
                is_approved = None  # 需要人工审核
            elif correlation_conclusion == "完全无关":
                is_approved = False
            else:
                is_approved = None  # 未能确定，需要人工审核

            # 将关联性结论添加到响应中
            response["correlation_conclusion"] = correlation_conclusion
            logger.info(f"从分析报告中提取的关联性结论: {correlation_conclusion}")

        # 获取数据来源(dev/prod)
        data_source = request.env if hasattr(request, "env") and request.env in ["dev", "prod"] else "prod"
        logger.info(f"数据来源: {data_source}")

        # 创建数据库记录对象
        db_record = CodeMergeAICheck(
            request_time=request_start_time,
            complete_time=current_time,
            requirement_info=requirement_info,
            commit_diff_info=commit_diff_info,
            cos_address=cos_address,
            analysis_report=analysis_report,
            analysis_success=analysis_success,
            is_approved=is_approved,
            correlation_conclusion=correlation_conclusion,
            data_source=data_source,  # 添加数据来源字段
            created_at=current_time
        )

        # 保存到数据库
        with get_session() as db_session:
            db_session.add(db_record)
            db_session.commit()  # 先提交以获取ID
            record_id = db_record.id  # 在会话关闭前获取ID
            logger.info(f"数据已成功保存到数据库，记录ID: {record_id}")

        # 将数据库存储结果添加到响应中
        response["db_storage"] = {
            "success": True,
            "record_id": record_id  # 使用之前保存的ID值
        }

    except Exception as e:
        logger.error(f"保存数据到数据库过程中出错: {str(e)}")
        # 添加错误信息到响应中，但不影响主要功能
        response["db_storage"] = {
            "success": False,
            "error": str(e)
        }

    # 执行回调通知
    logger.info(f"准备执行回调通知，UUID: {getattr(request, 'uuid', 'UUID字段不存在')}")
    logger.info(f"cos_result状态: {cos_result}")
    try:
        if cos_result and cos_result.get('url'):
            logger.info(f"开始发送回调，UUID: {request.uuid}, COS地址: {cos_result.get('url')}")
            await send_callback(request.uuid, cos_result.get('url'))
            logger.info(f"回调成功发送，UUID: {request.uuid}")
        else:
            logger.warning(f"没有COS地址，跳过回调，UUID: {getattr(request, 'uuid', 'UUID字段不存在')}, cos_result: {cos_result}")
    except Exception as e:
        logger.error(f"发送回调失败，UUID: {getattr(request, 'uuid', 'UUID字段不存在')}, 错误: {str(e)}")

    # 由于是异步执行，这里不再返回response
    logger.info(f"异步处理完成，总耗时: {datetime.now() - request_start_time}")
    return


@app.get("/")
async def root():
    # 检查是否有保存的数据
    has_descriptions = len(saved_descriptions) > 0
    has_branches = len(saved_branches) > 0
    has_commit_diffs = len(saved_commit_diffs) > 0

    return {
        "message": "需求与提交差异API服务正在运行",
        "status": "ok",
        "features": [
            "支持HTML格式需求描述中的文本和图片解析",
            "使用多模态大模型(InternVL3-38B)分析需求描述和图片",
            "关联代码提交差异与需求分析",
            "生成综合分析报告"
        ],
        "available_endpoints": [
            "/api/requirements (POST)"
        ],
        "saved_data_status": {
            "descriptions": f"已保存 {len(saved_descriptions)} 个需求描述" if has_descriptions else "无数据",
            "branches": f"已保存 {len(saved_branches)} 个分支信息" if has_branches else "无数据",
            "commit_diffs": "已保存提交差异数据" if has_commit_diffs else "无数据"
        }
    }


if __name__ == "__main__":
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="需求与GitLab提交差异API服务")
        parser.add_argument("--port", type=int, default=5002, help="服务运行端口, 默认8000")
        parser.add_argument("--auto-port", action="store_true", help="如果指定端口被占用，自动寻找可用端口")
        parser.add_argument("--host", default="0.0.0.0", help="服务监听地址, 默认0.0.0.0")
        args = parser.parse_args()

        # 检查端口是否可用
        port = args.port

        # 输出启动信息
        print("\n=== 服务正在启动 ===")
        print(f"访问 http://localhost:{port}/ 检查服务状态")
        print(f"POST接口: http://localhost:{port}/api/requirements")
        print(f"API文档: http://localhost:{port}/docs")
        print(f"分析报告将保存在: {os.path.join(os.getcwd(), 'analysis_reports')} 目录下")
        print("===================\n")

        # 确保analysis_reports目录存在
        os.makedirs("analysis_reports", exist_ok=True)

        # 启动服务
        uvicorn.run("webhook_service:app", host=args.host, port=port, reload=True)
    except Exception as e:
        print(f"启动服务失败: {str(e)}")

