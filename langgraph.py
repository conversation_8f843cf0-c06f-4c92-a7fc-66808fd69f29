"""
LangGraph 需求分析系统
基于大模型自主决策的高效需求文档分析流程
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, TypedDict
from datetime import datetime

from dotenv import load_dotenv
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import InMemorySaver
from langchain_mcp_adapters.client import MultiServerMCPClient
from litellm import Router
from langchain_litellm import ChatLiteLLMRouter
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AnalysisState(TypedDict):
    """分析状态定义"""
    requirement_doc: str  # 原始需求文档
    messages: List[Dict]  # 对话历史（包含搜索结果）
    search_count: int  # 搜索计数
    final_understanding: str  # 最终理解输出
    is_complete: bool  # 是否完成分析
    needs_final_analysis: bool  # 是否需要最终分析
    search_queries: List[str]  # 搜索查询历史
    model_responses: List[Dict]  # 保存每次大模型的完整响应


class OptimizedRequirementAnalysis:
    """精简版需求分析图类"""

    def __init__(self):
        self.checkpointer = InMemorySaver()
        self.mcp_client = None
        self.model = None
        self.tools = []
        self.graph = None

        # 系统提示词
        self.system_prompt = """你是一个专业的代码分析助手，专门分析需求文档并深入理解相关代码库。

工作流程：
1. **第一阶段**：接收需求文档，将其完整且严谨地翻译成英文，并用英文进行第一次搜索
2. **第二阶段**：基于搜索结果，持续生成不同角度的英文查询，深度探索相关代码
3. **终止条件**：当你认为已经收集到足够的代码片段，并对需求文档有充分认知时，明确说明分析完成

重要规则：
- 必须至少进行2轮以上的搜索
- 每次搜索都要使用不同的英文关键词和角度
- 持续搜索直到你认为对需求文档有足够深入的理解
- **关键**：如果你需要搜索，必须立即调用search_codebase工具，不要只描述计划
- **工具调用格式**：直接调用工具，不要在思考标签中描述

当前搜索次数：{search_count}

可用工具：
- search_codebase(query: str) - 搜索代码库中的相关代码，query必须是英文
"""

    async def initialize(self):
        """初始化MCP客户端和模型"""
        try:
            # 1. 加载服务器配置 - 使用异步文件读取
            servers_config_path = "/Users/<USER>/Downloads/langgraph_learning/data_agent/search_codebase.json"

            # 使用 asyncio.to_thread 来异步执行文件读取
            def read_config():
                with open(servers_config_path, "r", encoding="utf-8") as f:
                    return json.load(f).get("mcpServers", {})

            servers_cfg = await asyncio.to_thread(read_config)

            # 2. 连接MCP服务器
            self.mcp_client = MultiServerMCPClient(servers_cfg)
            self.tools = await self.mcp_client.get_tools()
            logger.info(f"✅ 已加载 {len(self.tools)} 个 MCP 工具： {[t.name for t in self.tools]}")

            # 3. 初始化模型
            model_list = [
                {
                    "model_name": "Qwen3-30B-A3B",
                    "litellm_params": {
                        "model": "openai/Qwen3-30B-A3B",
                        "api_key": "",
                        "api_base": "",
                        "max_tokens": 30000,  # 设置最大输出token数
                    },
                }
            ]

            litellm_router = Router(model_list=model_list)
            self.model = ChatLiteLLMRouter(
                router=litellm_router,
                model_name="Qwen3-30B-A3B",
                temperature=0.1,
                max_tokens=30000,  # 确保输出足够长
            )

            logger.info("✅ 模型初始化完成")

        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            raise

    def create_initial_state(self, requirement_doc: str) -> AnalysisState:
        """创建初始状态"""
        system_message = self.system_prompt.format(search_count=0)
        return AnalysisState(
            requirement_doc=requirement_doc,
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": f"需求文档：{requirement_doc}\n\n请按照工作流程分析这个需求文档。"}
            ],
            search_count=0,
            final_understanding="",
            is_complete=False,
            needs_final_analysis=False,
            search_queries=[],
            model_responses=[]
        )

    async def analysis_and_search_node(self, state: AnalysisState) -> AnalysisState:
        """分析搜索节点：大模型自主决策 - 分析、搜索或结束"""
        logger.info(f"🔍 执行分析搜索节点 (搜索次数: {state['search_count']})")

        try:
            # 准备工具定义
            available_tools = []
            for tool in self.tools:
                try:
                    # 获取工具schema
                    if hasattr(tool, 'args_schema') and tool.args_schema:
                        if hasattr(tool.args_schema, 'schema'):
                            parameters = tool.args_schema.schema()
                        elif hasattr(tool.args_schema, 'model_json_schema'):
                            parameters = tool.args_schema.model_json_schema()
                        else:
                            parameters = {}
                    else:
                        parameters = {}

                    tool_def = {
                        "type": "function",
                        "function": {
                            "name": tool.name,
                            "description": tool.description,
                            "parameters": parameters
                        }
                    }
                    available_tools.append(tool_def)
                except Exception as e:
                    logger.warning(f"⚠️ 跳过工具 {tool.name}: {e}")
                    continue

            # 更新系统提示词中的搜索次数
            updated_messages = state["messages"].copy()
            # print("updated_messages", updated_messages)
            if updated_messages[0]["role"] == "system":
                updated_messages[0]["content"] = self.system_prompt.format(search_count=state["search_count"])

            # 转换消息格式并进行上下文管理
            messages = []
            for msg in updated_messages:
                if msg["role"] == "system":
                    messages.append(SystemMessage(content=msg["content"]))
                elif msg["role"] == "user":
                    messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    messages.append(AIMessage(content=msg["content"]))
                elif msg["role"] == "tool":
                    # 工具消息需要特殊处理
                    messages.append(ToolMessage(
                        content=msg['content'],
                        tool_call_id=msg.get('tool_call_id', 'unknown')
                    ))
            # print("messages: ", messages)

            # 调用模型 - 绑定工具
            # model_with_tools = self.model.bind(tools=available_tools)
            model_with_tools = self.model.bind_tools(self.tools)
            response = await model_with_tools.ainvoke(messages)

            # 🔍 打印模型原始响应信息 - 用于调试
            print("=" * 80)
            print("🤖 模型原始响应信息:")
            print(f"response: {response}")
            # print(f"response.content: {response.content}")
            # with open(f"model_response_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}.txt", "w", encoding="utf-8") as f:
            #     f.write(str(response))
            #print(f"response.tool_calls: {getattr(response, 'tool_calls', 'None')}")
            # print(f"response.response_metadata: {getattr(response, 'response_metadata', 'None')}")
            # if hasattr(response, 'response_metadata'):
            #     print(f"finish_reason from metadata: {response.response_metadata.get('finish_reason', 'Not found')}")
            print("=" * 80)

            # 保存完整的模型响应
            response_data = {
                "content": response.content,
                "finish_reason": getattr(response, 'response_metadata', {}).get('finish_reason', 'unknown'),
                "tool_calls": getattr(response, 'tool_calls', []),
                "search_count": state["search_count"]
            }
            state["model_responses"].append(response_data)

            # print("state['model_responses']",state['model_responses'])

            logger.info(
                f"📝 保存模型响应 #{len(state['model_responses'])}, finish_reason: {response_data['finish_reason']}")

            # 根据 finish_reason 判断下一步行为
            finish_reason = response_data['finish_reason']
            has_tool_calls = hasattr(response, 'tool_calls') and response.tool_calls and len(response.tool_calls) > 0

            # 增强调试信息
            logger.info(f"🔍 finish_reason: {finish_reason}, has_tool_calls: {has_tool_calls}")
            if has_tool_calls:
                logger.info(f"🔧 工具调用详情: {[tc.get('name', 'unknown') for tc in response.tool_calls]}")

            if finish_reason == 'tool_calls' or has_tool_calls:
                # finish_reason 为 tool_calls 或有工具调用，继续执行工具
                tool_call = response.tool_calls[0]

                # 如果是搜索工具，记录查询
                if tool_call["name"] == "search_codebase" and "query" in tool_call["args"]:
                    state["search_queries"].append(tool_call["args"]["query"])

                state["messages"].append({
                    "role": "assistant",
                    "content": response.content,
                    "tool_calls": [{
                        "id": tool_call.get("id", f"call-{state['search_count']}"),
                        "type": "function",
                        "function": {
                            "name": tool_call["name"],
                            "arguments": json.dumps(tool_call["args"])
                        }
                    }]
                })

                logger.info(f"🔧 大模型决定调用工具: {tool_call['name']} (finish_reason: {finish_reason})")

                # 检查是否达到最大搜索次数
                max_searches = 6  # 设置最大搜索次数
                if state["search_count"] >= max_searches - 1:  # 减1是因为当前搜索还未执行
                    logger.warning(f"⚠️ 即将达到最大搜索次数 {max_searches}，标记需要最终分析")
                    state["needs_final_analysis"] = True

            elif finish_reason == 'stop':
                # finish_reason 为 stop，模型认为可以停止
                state["messages"].append({
                    "role": "assistant",
                    "content": response.content
                })

                # 判断是否应该结束
                if state["search_count"] < 2:
                    # 强制至少2轮搜索
                    force_prompt = f"你只进行了{state['search_count']}次搜索，这还不够深入。请立即调用search_codebase工具来搜索更多相关代码。不要只是描述，直接调用工具。"
                    state["messages"].append({"role": "user", "content": force_prompt})
                    logger.info("🔄 强制继续搜索（未达到最小搜索次数）")
                else:
                    # 模型认为已经完成，标记为完成状态
                    state["is_complete"] = True
                    state["needs_final_analysis"] = True  # 需要基于所有上下文生成最终分析
                    logger.info("✅ 大模型表示分析完成 (finish_reason: stop)")

            else:
                # 其他情况，继续分析
                state["messages"].append({
                    "role": "assistant",
                    "content": response.content
                })

                continue_prompt = "请继续生成不同角度的英文查询来深入探索相关代码，或者如果你认为已经收集到足够信息，请停止搜索。"
                state["messages"].append({"role": "user", "content": continue_prompt})
                logger.info(f"🔄 继续分析 (finish_reason: {finish_reason})")

        except Exception as e:
            logger.error(f"❌ 分析搜索节点错误: {e}")
            state["is_complete"] = True
            state["needs_final_analysis"] = True
            state["final_understanding"] = f"分析过程中出现错误: {str(e)}"
        # print("state", state)
        return state

    async def tool_execution_node(self, state: AnalysisState) -> AnalysisState:
        """工具执行节点：执行MCP搜索工具"""
        logger.info("🔧 执行工具调用节点")

        try:
            # 获取最后一条助手消息中的工具调用
            last_message = state["messages"][-1]
            if last_message.get("role") == "assistant" and "tool_calls" in last_message:
                tool_call = last_message["tool_calls"][0]
                tool_name = tool_call["function"]["name"]
                tool_args = json.loads(tool_call["function"]["arguments"])
                tool_call_id = tool_call["id"]

                logger.info(f"🔍 执行搜索: {tool_args.get('query', 'N/A')}")

                # 执行工具调用
                result = None
                for tool in self.tools:
                    if tool.name == tool_name:
                        result = await tool.ainvoke(tool_args)
                        break

                if result:
                    # 更新搜索计数
                    state["search_count"] += 1

                    # 添加工具结果到消息历史
                    state["messages"].append({
                        "role": "tool",
                        "content": str(result),
                        "tool_call_id": tool_call_id
                    })
                    #print("state[messages]",state["messages"])
                    logger.info(f"📊 搜索进度: {state['search_count']}")

                else:
                    logger.error(f"❌ 未找到工具: {tool_name}")
                    state["messages"].append({
                        "role": "tool",
                        "content": f"工具调用失败: 未找到工具 {tool_name}",
                        "tool_call_id": tool_call_id
                    })

        except Exception as e:
            logger.error(f"❌ 工具执行错误: {e}")
            state["messages"].append({
                "role": "tool",
                "content": f"工具执行失败: {str(e)}",
                "tool_call_id": "error"
            })

        return state

    async def final_analysis_node(self, state: AnalysisState) -> AnalysisState:
        """最终分析节点：基于所有模型响应生成综合分析报告"""
        logger.info("📋 执行最终分析节点")

        try:
            # 构建包含所有上下文的最终分析提示词
            search_results = []

            for i, msg in enumerate(state["messages"]):
                if msg.get("role") == "tool":
                    search_results.append(f"search result {len(search_results) + 1}:\n{msg['content']}")

            all_model_responses = "\n\n".join(search_results) if search_results else "未找到任何搜索结果"
            print("all_model_responses", all_model_responses)

            analysis_prompt = f"""
基于你通过自主搜索获得的所有信息，请展示你对需求文档和相关代码库的深刻认知：

原始需求文档:
{state['requirement_doc']}

总搜索次数: {state['search_count']}
搜索查询历史: {', '.join(state['search_queries'])}

你获得的完整上下文:
{all_model_responses}

请基于你的深度理解，展示你的认知成果：

## 1. 需求文档的深度认知
展示你对需求文档核心意图、关键概念、技术要点的深入理解和洞察

## 2. 代码库的深度认知
展示你通过搜索发现的代码库架构、关键模块、技术特征的深层理解

## 3. 需求与代码的关联认知
展示你对需求文档与现有代码库之间深层关联关系的认知和理解

请充分体现你通过自主搜索获得的深刻认知，展现对需求文档和代码库的全面、深入的理解洞察。
"""

            # 创建新的消息上下文，只包含系统提示和最终分析请求
            final_messages = [
                SystemMessage(content="你是一个专业的代码分析助手，请基于提供的所有上下文信息进行综合分析。"),
                HumanMessage(content=analysis_prompt)
            ]

            # 调用模型生成最终分析（不带工具）
            response = await self.model.ainvoke(final_messages)

            # 保存最终分析结果和响应信息
            final_response_data = {
                "content": response.content,
                "finish_reason": getattr(response, 'response_metadata', {}).get('finish_reason', 'unknown'),
                "is_final_analysis": True
            }
            state["model_responses"].append(final_response_data)

            state["final_understanding"] = response.content
            state["messages"].append({
                "role": "assistant",
                "content": response.content
            })

            # 标记完成
            state["is_complete"] = True
            state["needs_final_analysis"] = False

            logger.info("✅ 最终分析完成，基于所有上下文生成综合报告")

        except Exception as e:
            logger.error(f"❌ 最终分析节点错误: {e}")
            state["final_understanding"] = f"最终分析过程中出现错误: {str(e)}"
            state["is_complete"] = True
            state["needs_final_analysis"] = False

        return state

    def route_next_step(self, state: AnalysisState) -> str:
        """路由函数：决定下一步流程"""
        # 检查是否需要最终分析
        if state["is_complete"] and state["needs_final_analysis"]:
            logger.info("📋 需要最终分析，转入最终分析节点")
            return "final_analysis"

        # 检查是否完全结束
        if state["is_complete"] and not state["needs_final_analysis"]:
            logger.info("🏁 分析完成，结束流程")
            return END

        # 检查是否达到最大搜索次数（安全限制）
        max_searches = 6  # 设置最大搜索次数
        if state["search_count"] >= max_searches:
            logger.warning(f"⚠️ 达到最大搜索次数 {max_searches}，转入最终分析")
            state["is_complete"] = True
            state["needs_final_analysis"] = True
            return "final_analysis"

        # 检查最后一条消息是否有工具调用
        if state["messages"] and state["messages"][-1].get("tool_calls"):
            logger.info("🔧 检测到工具调用，转到工具执行节点")
            return "tool_execution"  # 有工具调用→执行工具
        else:
            logger.info("🔍 继续分析搜索")
            return "analysis_and_search"  # 无工具调用→继续分析

    def build_graph(self) -> StateGraph:
        """构建LangGraph图"""
        # 创建状态图
        workflow = StateGraph(AnalysisState)

        # 添加节点
        workflow.add_node("analysis_and_search", self.analysis_and_search_node)
        workflow.add_node("tool_execution", self.tool_execution_node)
        workflow.add_node("final_analysis", self.final_analysis_node)

        # 设置入口点
        workflow.add_edge(START, "analysis_and_search")

        # 设置条件路由
        workflow.add_conditional_edges(
            "analysis_and_search",
            self.route_next_step,
            {
                "tool_execution": "tool_execution",
                "analysis_and_search": "analysis_and_search",
                "final_analysis": "final_analysis",
            }
        )

        # 工具执行后的路由
        workflow.add_conditional_edges(
            "tool_execution",
            self.route_next_step,
            {
                "analysis_and_search": "analysis_and_search",
                "final_analysis": "final_analysis",

            }
        )

        # 最终分析后结束
        workflow.add_edge("final_analysis", END)

        # 编译图
        return workflow.compile(checkpointer=self.checkpointer)


# 全局实例
analysis_graph = OptimizedRequirementAnalysis()


async def get_graph(config=None):
    """为LangGraph提供graph实例 - 用于langgraph dev部署"""
    try:
        await analysis_graph.initialize()
        return analysis_graph.build_graph()
    except Exception as e:
        logger.error(f"❌ 图初始化失败: {e}")
        raise


# 导出给LangGraph使用的graph变量
graph = get_graph





async def chat_loop():
    """主交互循环"""
    try:
        # 初始化
        await analysis_graph.initialize()

        # 构建图
        compiled_graph = analysis_graph.build_graph()

        print("\n🤖 LangGraph 需求文档分析系统已启动!")
        print("📝 请输入需求文档，我会进行深入的代码分析")
        print("⚡ 输入 'quit' 退出，输入 'test' 运行测试")
        print("=" * 60)

        while True:
            try:
                requirement_doc = input("\n📋 请输入需求文档: ").strip()

                if requirement_doc.lower() == "quit":
                    print("👋 再见！")
                    break

                if not requirement_doc:
                    print("❓ 请输入需求文档内容")
                    continue

                print(f"\n🔍 开始分析需求文档...")
                print(f"📄 需求内容: {requirement_doc}")

                # 创建初始状态
                initial_state = analysis_graph.create_initial_state(requirement_doc)

                # 配置 - 增加递归限制
                config = {
                    "configurable": {"thread_id": f"session-{datetime.now().strftime('%Y%m%d_%H%M%S')}"},
                    "recursion_limit": 100  # 设置递归限制为100
                }

                # 运行图
                result = await compiled_graph.ainvoke(initial_state, config=config)

                # 显示结果
                if result.get("final_understanding"):
                    print(f"\n📊 最终分析结果:")
                    print("=" * 60)
                    print(result["final_understanding"])
                    print("=" * 60)
                    print(f"🔍 总搜索次数: {result['search_count']}")

                    # 保存分析结果到文件
                    output_file = f"analysis_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    analysis_data = {
                        "requirement_doc": result["requirement_doc"],
                        "search_count": result["search_count"],
                        "final_understanding": result["final_understanding"],
                        "timestamp": datetime.now().isoformat(),
                        "messages_count": len(result["messages"]),
                        "search_queries": result["search_queries"],
                        "model_responses": result["model_responses"],
                        "total_model_responses": len(result["model_responses"])
                    }

                    # def save_analysis():
                    #     with open(output_file, "w", encoding="utf-8") as f:
                    #         json.dump(analysis_data, f, ensure_ascii=False, indent=2)
                    #
                    # await asyncio.to_thread(save_analysis)
                    # print(f"📝 分析结果已保存到: {output_file}")
                else:
                    print(f"\n📊 分析过程完成，搜索了 {result['search_count']} 次")

            except KeyboardInterrupt:
                print("\n👋 用户中断，再见！")
                break
            except Exception as e:
                logger.error(f"❌ 分析过程中出现错误: {e}")
                print(f"❌ 错误: {e}")

    except Exception as e:
        logger.error(f"❌ 系统初始化失败: {e}")
        print(f"❌ 系统初始化失败: {e}")


async def analyze_requirement_document(requirement_doc: str, thread_id: Optional[str] = None) -> Dict:
    """
    封装的需求文档分析函数，可在其他文件中导入使用

    Args:
        requirement_doc (str): 需求文档内容
        thread_id (Optional[str]): 线程ID，用于会话管理，默认自动生成

    Returns:
        Dict: 分析结果，包含以下字段：
            - requirement_doc: 原始需求文档
            - search_queries: 搜索查询历史
            - tool_results: messages中role为"tool"的content内容列表
            - final_understanding: 最终分析结果

    Usage:
        from langgraph.test_langgraph import analyze_requirement_document

        result = await analyze_requirement_document("您的需求文档内容")
        print(result["final_understanding"])
    """
    try:
        # 1. 初始化 MCP 客户端和模型
        logger.info("🚀 开始初始化需求分析系统...")
        await analysis_graph.initialize()

        # 2. 构建 LangGraph 图
        logger.info("🔧 构建 LangGraph 图...")
        compiled_graph = analysis_graph.build_graph()

        # 3. 创建初始状态
        logger.info("📋 创建初始状态...")
        initial_state = analysis_graph.create_initial_state(requirement_doc)

        # 4. 配置执行参数
        if thread_id is None:
            thread_id = f"session-{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"

        config = {
            "configurable": {"thread_id": thread_id},
            "recursion_limit": 100  # 设置递归限制
        }

        # 5. 执行图分析
        logger.info(f"🔍 开始执行需求文档分析 (线程ID: {thread_id})...")
        result = await compiled_graph.ainvoke(initial_state, config=config)

        # 6. 构建返回结果 - 只保留核心信息
        # 提取messages中role为tool的content内容
        tool_contents = []
        for msg in result["messages"]:
            if msg.get("role") == "tool" and "content" in msg:
                tool_contents.append(msg["content"])

        analysis_result = {
            "requirement_doc": result["requirement_doc"],
            "search_queries": result["search_queries"],
            "tool_results": tool_contents,  # messages中role为tool的content内容
            "final_understanding": result["final_understanding"]
        }

        logger.info(f"✅ 需求文档分析完成！搜索次数: {result['search_count']}")
        return analysis_result

    except Exception as e:
        logger.error(f"❌ 需求文档分析失败: {e}")
        # 返回错误信息
        return {
            "requirement_doc": requirement_doc,
            "search_queries": [],
            "tool_results": [],
            "final_understanding": f"分析过程中出现错误: {str(e)}"
        }


async def main():
    """主函数"""
    await chat_loop()


if __name__ == "__main__":
    asyncio.run(main())
